import { Card, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"
import Link from "next/link"

export function Certificates() {
  const certificates = [
    {
      id: 1,
      title: "Java Programming",
      issuer: "NPTEL",
      date: "April 2025",
      credential: "NPTEL25CS57S1143400074",
      url: "/NPTEL-java.jpg",
    },
    {
      id: 2,
      title: "Python OOP",
      issuer: "Udemy",
      date: "January 2025",
      credential: "http://ude.my/UC-fa960b91-8faa-44ce-b0a5-37c9629daed6",
      url: "/py-udemy.jpg",
    },
    {
      id: 3,
      title: "Machine Learning",
      issuer: "NPTEL",
      date: "Jan-March 2025",
      credential: "NPTEL25CS50S436600158",
      url: "/ML certification.jpg",
    },
    {
      id: 4,
      title: "Web Development",
      issuer: "TeachNook",
      date: "December 2023 - January 2024",
      credential: "TNINTC24-322",
      url: "/Teachnook COURSE Completion Certificate _ Shreyanka A Y_page-0001.jpg",
    },
    {
      id: 5,
      title: "SQL Bootcamp",
      issuer: "Let's Upgrade",
      date: "October 2024",
      credential: "LUESQLoct124178",
      url: "/LUESQLOCT124178 (3)_page-0001.jpg",
    },
  ]

  return (
    <section className="py-12 sm:py-20 px-4 bg-[rgb(236,250,229)] dark:bg-[rgb(34,34,34)]">
      <div className="container mx-auto max-w-5xl">
        <h2 className="text-2xl sm:text-3xl font-bold mb-8 sm:mb-12 text-center">Certificates</h2>
        <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
          {certificates.map((certificate) => (
            <Card key={certificate.id} className="h-full bg-[rgb(236,250,229)] dark:bg-[rgb(34,34,34)] border-2 border-[rgb(195,210,185)] dark:border-[rgb(60,60,60)]">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg sm:text-xl">{certificate.title}</CardTitle>
                <CardDescription className="text-sm">
                  Issued by {certificate.issuer} • {certificate.date}
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-3">
                <p className="text-xs sm:text-sm text-muted-foreground break-all">Credential ID: {certificate.credential}</p>
              </CardContent>
              <CardFooter>
                <Button asChild variant="outline" size="sm" className="w-full sm:w-auto bg-[rgb(236,250,229)] dark:bg-[rgb(34,34,34)] border-2 border-[rgb(195,210,185)] dark:border-[rgb(60,60,60)] hover:bg-[rgb(236,250,229)] dark:hover:bg-[rgb(34,34,34)] text-black dark:text-white">
                  <Link href={certificate.url}>
                    <ExternalLink className="mr-2 h-4 w-4" /> View Certificate
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
