import { Gith<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import Link from "next/link"

export function Footer() {
  return (
    <footer className="bg-[rgb(236,250,229)] dark:bg-[rgb(34,34,34)] py-10 px-4">
      <div className="container mx-auto max-w-5xl">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-6 md:mb-0">
            <h3 className="text-xl font-bold">Shreyanka A Y</h3>
            <p className="text-muted-foreground">Pre Final Year Student</p>
          </div>

          <div className="flex gap-4 mb-6 md:mb-0">
            <Link href="https://github.com/Shreyanka-A-Y" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary transition-colors">
              <Github className="h-5 w-5" />
              <span className="sr-only">GitHub</span>
            </Link>
            <Link href="http://www.linkedin.com/in/shreyankaay" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary transition-colors">
              <Linkedin className="h-5 w-5" />
              <span className="sr-only">LinkedIn</span>
            </Link>
            <Link href="mailto:<EMAIL>" className="text-muted-foreground hover:text-primary transition-colors">
              <Mail className="h-5 w-5" />
              <span className="sr-only">Email</span>
            </Link>
          </div>

          <div className="text-center md:text-right text-sm text-muted-foreground">
            <p>© {new Date().getFullYear()} Shreyanka A Y. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  )
}
