import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'Shreyanka AY Portfolio',
  description: 'Portfolio of Shreyanka AY - Software Developer',
  generator: 'v0.dev',
  icons: {
    icon: '/favicon.svg',
    shortcut: '/favicon.svg',
    apple: '/pic.jpg',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body>{children}</body>
    </html>
  )
}
