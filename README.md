# 🌟 Shreyanka AY Portfolio

A modern, responsive portfolio website built with Next.js, showcasing my projects, skills, and achievements as a Computer Science student.

## 🚀 Live Demo

[View Live Portfolio](https://shreyanka-portfolio.vercel.app) *(Update with your actual Vercel URL)*

## 📋 Table of Contents

- [About](#about)
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Sections](#sections)
- [Installation](#installation)
- [Environment Variables](#environment-variables)
- [Deployment](#deployment)
- [Contact](#contact)

## 🎯 About

This portfolio represents my journey as a pre-final year Computer Science student at Adichunchanagiri Institute of Technology. It showcases my projects, technical skills, academic achievements, and provides a way for potential employers and collaborators to connect with me.

## ✨ Features

- **🎨 Modern Design**: Clean, elegant interface with alternating background colors
- **🌓 Dark/Light Mode**: Theme toggle for better user experience
- **📱 Fully Responsive**: Works seamlessly across all devices
- **⚡ Fast Performance**: Built with Next.js for optimal loading speeds
- **📧 Contact Form**: Functional contact form with email integration
- **🎯 SEO Optimized**: Proper meta tags and structured data
- **♿ Accessible**: WCAG compliant design
- **🔒 Private Repository**: Code hosted securely on GitHub

## 🛠️ Tech Stack

- **Framework**: Next.js 15.2.4
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI + Custom Components
- **Icons**: Lucide React
- **Email Service**: Nodemailer with Gmail
- **Deployment**: Vercel
- **Version Control**: Git & GitHub

## 📄 Sections

### 1. **Hero Section**
- Personal introduction and profile picture
- Quick access to LinkedIn and resume download
- Academic information and graduation timeline

### 2. **About Me**
- Detailed personal introduction
- Educational background with CGPA and achievements
- Career objectives and interests

### 3. **Skills**
- Technical skills with clean visual presentation
- Programming languages, frameworks, and tools
- Database and development technologies

### 4. **Certificates**
- Professional certifications and course completions
- Visual certificate gallery with descriptions
- Links to credential verification

### 5. **Achievements**
- Hackathon participations and project showcases
- Academic and technical accomplishments
- Competition results and recognitions

### 6. **Projects**
- **Face Recognition System**: Real-time face recognition using FaceNet and OpenCV
- **Namma Nadu Travel Guide**: Interactive Karnataka tourism website
- **Sustainable Urban Living**: ACU Hackathon project for eco-friendly solutions
- **Blood Donation Management**: Healthcare platform for donation processes

### 7. **Contact**
- Functional contact form with email integration
- Direct contact information
- Social media links

### 8. **Footer**
- Social media links (GitHub, LinkedIn, Email)
- Copyright information
- Academic status

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Shreyanka-A-Y/Shreyanka-AY.git
   cd Shreyanka-AY
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   Fill in your email credentials (see Environment Variables section)

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🔐 Environment Variables

Create a `.env.local` file in the root directory:

```env
# Email Configuration for Contact Form
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Note: For Gmail, you need to:
# 1. Enable 2-factor authentication
# 2. Generate an "App Password" (not your regular password)
# 3. Use the app password above
```

## 🌐 Deployment

### Vercel (Recommended)

1. **Push to GitHub** (already done)
2. **Connect to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Add environment variables
   - Deploy

3. **Automatic Deployments**:
   - Every push to main branch triggers auto-deployment
   - Preview deployments for pull requests

### Other Platforms

- **Netlify**: Connect GitHub repo and deploy
- **Railway**: For full-stack deployment with database
- **GitHub Pages**: Static export (limited functionality)

## 📊 Performance

- **Lighthouse Score**: 95+ across all metrics
- **Core Web Vitals**: Optimized for excellent user experience
- **SEO**: Structured data and meta tags for search engines
- **Accessibility**: WCAG 2.1 AA compliant

## 🤝 Contributing

While this is a personal portfolio, suggestions and feedback are welcome!

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/improvement`)
3. Commit your changes (`git commit -am 'Add improvement'`)
4. Push to the branch (`git push origin feature/improvement`)
5. Open a Pull Request

## 📞 Contact

**Shreyanka A Y**
- 📧 Email: [<EMAIL>](mailto:<EMAIL>)
- 💼 LinkedIn: [linkedin.com/in/shreyankaay](http://www.linkedin.com/in/shreyankaay)
- 🐱 GitHub: [github.com/Shreyanka-A-Y](https://github.com/Shreyanka-A-Y)

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- **Next.js Team** for the amazing framework
- **Vercel** for seamless deployment
- **Tailwind CSS** for utility-first styling
- **Radix UI** for accessible components
- **Lucide** for beautiful icons

---

**Made with ❤️ by Shreyanka A Y**  
*Pre-final year Computer Science Student*  
*Adichunchanagiri Institute of Technology, Chikkamagaluru*
