import { Code, Database, FileCode2, Braces, PenTool, Brain, Cloud, GitBranch, ChartBar, BookDashed } from "lucide-react"

export function Skills() {
  const skills = [
    {
      icon: <FileCode2 className="h-8 w-8" />,
      title: "Python",
    },
    {
      icon: <Braces className="h-8 w-8" />,
      title: "Java",
    },
    {
      icon: <Code className="h-8 w-8" />,
      title: "HTML/CSS",
    },
    {
      icon: <BookDashed className="h-8 w-8" />,
      title: "Flask",
    },
    {
      icon: <PenTool className="h-8 w-8" />,
      title: "JavaScript",
    },
    {
      icon: <Brain className="h-8 w-8" />,
      title: "Machine Learning",
    },
    {
      icon: <ChartBar className="h-8 w-8" />,
      title: "Power BI",
    },
    {
      icon: <Database className="h-8 w-8" />,
      title: "SQL",
    },
    {
      icon: <Cloud className="h-8 w-8" />,
      title: "<PERSON><PERSON>",
    },
    {
      icon: <GitBranch className="h-8 w-8" />,
      title: "Git",
    },
  ]

  return (
    <section className="py-12 sm:py-20 px-4 bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)]">
      <div className="container mx-auto max-w-5xl">
        <h2 className="text-2xl sm:text-3xl font-bold mb-8 sm:mb-12 text-center">My Skills</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
          {skills.map((skill, index) => (
            <div
              key={index}
              className="bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] p-4 sm:p-6 rounded-lg border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)] shadow-sm hover:shadow-md transition-shadow text-center"
            >
              <div className="text-primary mb-3 sm:mb-4 flex justify-center">{skill.icon}</div>
              <h3 className="text-lg sm:text-xl font-semibold">{skill.title}</h3>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
