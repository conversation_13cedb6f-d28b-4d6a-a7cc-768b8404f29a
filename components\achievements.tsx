import { Trophy, Users } from "lucide-react"

export function Achievements() {
  const achievements = [
    {
      id: 1,
      category: "Hackathon Participation",
      icon: Trophy,
      items: [
        {
          title: "Advaya Hackathon",
          description: "Participated in innovative technology solutions competition"
        },
        {
          title: "Ignitex Hackathon, BGSIT Mandya",
          description: "Secured 4th place in competitive programming and development challenge"
        },
        {
          title: "Code4Change Hackathon",
          description: "Secured Best in Domain award in Sustainability Track for exceptional technical solution"
        }
      ]
    },
    {
      id: 2,
      category: "Professional Involvement",
      icon: Users,
      items: [
        {
          title: "IEEE International Conference (ICAIT-24)",
          description: "Session volunteer at the Second IEEE International Conference organized by Department of Computer Science and Engineering, AIT Chikkamagaluru"
        },
        {
          title: "Namma Idea – Startup Pitching Event",
          description: "Participated in the Startup Karnataka-supported Idea Pitching Event organized by ME-RIISE Foundation at Malnad College of Engineering, Hassan"
        }
      ]
    }
  ]

  return (
    <section className="py-12 sm:py-20 px-4 bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)]">
      <div className="container mx-auto max-w-5xl">
        <h2 className="text-2xl sm:text-3xl font-bold mb-8 sm:mb-12 text-center">Participation & Achievements</h2>
        <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
          {achievements.map((achievement) => (
            <div key={achievement.id} className="group">
              <div className="flex items-center gap-3 mb-4">
                <achievement.icon className="h-5 w-5 text-primary" />
                <h3 className="text-base sm:text-lg font-medium text-foreground">
                  {achievement.category}
                </h3>
              </div>
              <div className="space-y-3 sm:space-y-4">
                {achievement.items.map((item, index) => (
                  <div key={index} className="pl-6 sm:pl-8 border-l-2 border-border/30">
                    <h4 className="text-sm sm:text-base font-medium text-foreground mb-1">
                      {item.title}
                    </h4>
                    <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                      {item.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
