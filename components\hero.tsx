import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, Linked<PERSON> } from "lucide-react"
import Link from "next/link"

export function Hero() {
  return (
    <section className="py-12 sm:py-20 px-4 bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)]">
      <div className="container mx-auto max-w-5xl">
        <div className="grid md:grid-cols-2 gap-6 md:gap-10 items-center">
          <div className="aspect-square bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)] rounded-lg overflow-hidden max-w-sm mx-auto md:max-w-none">
            <img
              src="/pic.jpg"
              alt="Student Profile"
              className="w-full h-full object-cover"
            />
          </div>
          <div className="text-center md:text-left">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 text-primary">Shreyanka A Y</h1>
            <h2 className="text-lg sm:text-xl md:text-2xl font-medium text-muted-foreground mb-4">3rd Year Student</h2>
            <div className="space-y-4 mb-6">
              <div>
                <h3 className="font-medium">Degree</h3>
                <p className="text-muted-foreground">Bachelor of Engineering in Computer Science</p>
              </div>
              <div>
                <h3 className="font-medium">College</h3>
                <p className="text-muted-foreground">Adichunchanagiri Institutte of Technology, Chikkamagaluru-577102</p>
              </div>
              <div>
                <h3 className="font-medium">Expected Graduation</h3>
                <p className="text-muted-foreground">June 2026</p>
              </div>
              <div>
                <h3 className="font-medium">About Me</h3>
                <p className="text-muted-foreground">
                  I am a passionate pre-final year Computer Science student with a strong foundation in programming and
                  software development. I'm interested in machine learning, software development and web development, and I'm
                  seeking opportunities to apply my skills in a professional environment.
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center md:justify-start">
              <Button asChild variant="outline" className="w-full sm:w-auto bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)] hover:bg-[rgb(221,246,210)] dark:hover:bg-[rgb(0,0,0)] text-black dark:text-white">
                <Link href="http://www.linkedin.com/in/shreyankaay" target="_blank" rel="noopener noreferrer">
                  <Linkedin className="mr-2 h-4 w-4" /> LinkedIn Profile
                </Link>
              </Button>
              <Button asChild className="w-full sm:w-auto bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)] hover:bg-[rgb(221,246,210)] dark:hover:bg-[rgb(0,0,0)] text-black dark:text-white">
                <a href="/Shreyanka_A_Y_Resume.pdf" download="Shreyanka_A_Y_Resume.pdf" target="_blank" rel="noopener noreferrer">
                  <Download className="mr-2 h-4 w-4" /> Download Resume
                </a>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
