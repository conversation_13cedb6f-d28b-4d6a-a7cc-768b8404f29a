import { <PERSON> } from "@/components/hero"
import { About } from "@/components/about"
import { Skills } from "@/components/skills"
import { Certificates } from "@/components/certificates"
import { Achievements } from "@/components/achievements"
import { Projects } from "@/components/projects"
import { Contact } from "@/components/contact"
import { Footer } from "@/components/footer"
import { ThemeProvider } from "@/components/theme-provider"
import { ThemeToggle } from "@/components/theme-toggle"
import { Toaster } from "@/components/ui/toaster"

export default function Home() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="relative">
        <div className="fixed top-4 right-4 z-50">
          <ThemeToggle />
        </div>
        <Hero />
        <About />
        <Skills />
        <Certificates />
        <Achievements />
        <Projects />
        <Contact />
        <Footer />
      </div>
      <Toaster />
    </ThemeProvider>
  )
}
