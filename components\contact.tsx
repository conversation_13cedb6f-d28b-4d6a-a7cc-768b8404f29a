"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Mail, MapPin } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export function Contact() {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: "Message sent!",
          description: "Thank you for your message. I'll get back to you soon.",
        })
        setFormData({ name: "", email: "", subject: "", message: "" })
      } else {
        console.error("API Error:", data)
        throw new Error(data.details || data.error || "Failed to send message")
      }
    } catch (error) {
      console.error("Contact form error:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "There was an error sending your message. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <section id="contact" className="py-12 sm:py-20 px-4 bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)]">
      <div className="container mx-auto max-w-5xl">
        <h2 className="text-2xl sm:text-3xl font-bold mb-8 sm:mb-12 text-center">Contact Me</h2>

        <div className="grid md:grid-cols-2 gap-8 sm:gap-10">
          <div>
            <h3 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6">Contact Information</h3>
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)] p-3 rounded-full text-primary">
                  <Mail className="h-6 w-6" />
                </div>
                <div>
                  <h4 className="font-medium">Email</h4>
                  <p className="text-muted-foreground"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)] p-3 rounded-full text-primary">
                  <MapPin className="h-6 w-6" />
                </div>
                <div>
                  <h4 className="font-medium">Location</h4>
                  <p className="text-muted-foreground">Bangalore, India</p>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8 md:mt-0">
            <h3 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6">Send Me a Message</h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Input placeholder="Your Name" name="name" value={formData.name} onChange={handleChange} required className="bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)]" />
              </div>
              <div>
                <Input
                  type="email"
                  placeholder="Your Email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)]"
                />
              </div>
              <div>
                <Input placeholder="Subject" name="subject" value={formData.subject} onChange={handleChange} required className="bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)]" />
              </div>
              <div>
                <Textarea
                  placeholder="Your Message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  rows={5}
                  required
                  className="bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)]"
                />
              </div>
              <Button type="submit" className="w-full bg-[rgb(221,246,210)] dark:bg-[rgb(0,0,0)] border-2 border-[rgb(180,200,170)] dark:border-[rgb(40,40,40)] hover:bg-[rgb(221,246,210)] dark:hover:bg-[rgb(0,0,0)] text-black dark:text-white" disabled={isSubmitting}>
                {isSubmitting ? "Sending..." : "Send Message"}
              </Button>
            </form>
          </div>
        </div>
      </div>
    </section>
  )
}
