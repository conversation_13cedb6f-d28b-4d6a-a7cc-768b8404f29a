import { Github, ExternalLink } from "lucide-react"
import Link from "next/link"

export function Projects() {
  const projects = [
    {
      id: 1,
      title: "Face Recognition System",
      description: "Real-time face recognition using FaceNet and OpenCV with age, gender, and emotion detection capabilities.",
      tags: ["Python", "OpenCV", "Machine Learning"],
      githubUrl: "https://github.com/Shreyanka-A-Y/face-recognition",
      demoUrl: "#",
    },
    {
      id: 2,
      title: "Quiz Application - QuizMe",
      description: "QuizMe is a modular Java Swing-based quiz application featuring timed MCQs across multiple programming language topics.",
      tags: ["JAVA"],
      githubUrl: "https://github.com/Shreyanka-A-Y/QuizApplication-Java",
      demoUrl: "#",
    },
    {
      id: 3,
      title: "Carbon Emission Prediction",
      description: "Carbon emission prediction involves estimating future CO₂ levels based on historical data, energy usage, and economic indicators using machine learning or statistical models.",
      tags: ["Python", "Flask"],
      githubUrl: "https://github.com/Shreyanka-A-Y/Carbon-Emissions-Prediction.git",
      demoUrl: "#",
    },
    {
      id: 4,
      title: "Namma Nadu Travel Guide",
      description: "Interactive Karnataka tourism website showcasing destinations, cuisine, and cultural experiences.",
      tags: ["HTML", "CSS", "JavaScript", "PHP"],
      githubUrl: "https://github.com/Shreyanka-A-Y/namma-nadu-travel-guide",
      demoUrl: "#",
    },
    {
      id: 5,
      title: "Sustainable Urban Living",
      description: "ACU Hackathon project focused on promoting sustainable practices and eco-friendly solutions for urban environments.",
      tags: ["Web Development", "Sustainability", "Hackathon"],
      githubUrl: "https://github.com/Shreyanka-A-Y/ACU-Hackthon-team20",
      demoUrl: "#",
    },
    {
      id: 6,
      title: "Blood Donation Management",
      description: "Healthcare platform streamlining blood donation processes with donor-recipient matching and emergency medical features.",
      tags: ["TypeScript", "Next.js", "Healthcare"],
      githubUrl: "https://github.com/AdvayaHackathon/57.Team_Sirius",
      demoUrl: "#",
    },
  ]

  return (
    <section className="py-12 sm:py-20 px-4 bg-[rgb(236,250,229)] dark:bg-[rgb(34,34,34)]">
      <div className="container mx-auto max-w-5xl">
        <h2 className="text-2xl sm:text-3xl font-bold mb-8 sm:mb-12 text-center">Projects</h2>
        <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
          {projects.map((project) => (
            <div key={project.id} className="group p-4 sm:p-0">
              <h3 className="text-lg sm:text-xl font-medium text-foreground mb-2 group-hover:text-primary transition-colors">
                {project.title}
              </h3>
              <p className="text-muted-foreground text-sm sm:text-base mb-3 leading-relaxed">
                {project.description}
              </p>
              <div className="text-xs sm:text-sm text-muted-foreground mb-3">
                {project.tags.join(" • ")}
              </div>
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
                <Link
                  href={project.githubUrl}
                  className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                >
                  GitHub →
                </Link>
                {project.demoUrl && project.demoUrl !== "#" && (
                  <Link
                    href={project.demoUrl}
                    className="text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors"
                  >
                    Live Demo →
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
